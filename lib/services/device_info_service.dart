import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:network_info_plus/network_info_plus.dart';

class DeviceInfoService {
  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  final NetworkInfo _networkInfo = NetworkInfo();

  // Get device MAC address based on platform
  Future<String> getMacAddress() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        return await _getMobileDeviceMacAddress();
      } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
        return await _getDesktopDeviceMacAddress();
      } else {
        return 'unknown_device_mac';
      }
    } catch (e) {
      print('Error getting device MAC address: $e');
      return 'error_device_mac';
    }
  }

  // Get device ID based on platform (kept for backward compatibility)
  @Deprecated('Use getMacAddress() instead')
  Future<String> getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidDeviceId();
      } else if (Platform.isIOS) {
        return await _getIosDeviceId();
      } else if (Platform.isMacOS) {
        return await _getMacOsDeviceId();
      } else if (Platform.isWindows) {
        return await _getWindowsDeviceId();
      } else if (Platform.isLinux) {
        return await _getLinuxDeviceId();
      } else {
        return 'unknown_device';
      }
    } catch (e) {
      print('Error getting device ID: $e');
      return 'error_device_id';
    }
  }

  // Get MAC address for mobile devices (Android/iOS)
  Future<String> _getMobileDeviceMacAddress() async {
    try {
      // Try to get WiFi MAC address first
      final wifiMac = await _networkInfo.getWifiBSSID();
      if (wifiMac != null &&
          wifiMac.isNotEmpty &&
          wifiMac != '02:00:00:00:00:00') {
        return _formatMacAddress(wifiMac);
      }

      // Fallback to device-specific identifier if MAC is not available
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        return 'android_${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        return 'ios_${iosInfo.identifierForVendor ?? 'unknown'}';
      }

      return 'unknown_mobile_mac';
    } catch (e) {
      print('Error getting mobile MAC address: $e');
      return 'error_mobile_mac';
    }
  }

  // Get MAC address for desktop devices (macOS/Windows/Linux)
  Future<String> _getDesktopDeviceMacAddress() async {
    try {
      // Try to get WiFi MAC address first
      final wifiMac = await _networkInfo.getWifiBSSID();
      if (wifiMac != null &&
          wifiMac.isNotEmpty &&
          wifiMac != '02:00:00:00:00:00') {
        return _formatMacAddress(wifiMac);
      }

      // Fallback to device-specific identifier if MAC is not available
      if (Platform.isMacOS) {
        final macOsInfo = await _deviceInfoPlugin.macOsInfo;
        return 'macos_${macOsInfo.systemGUID ?? 'unknown'}';
      } else if (Platform.isWindows) {
        final windowsInfo = await _deviceInfoPlugin.windowsInfo;
        return 'windows_${windowsInfo.deviceId}';
      } else if (Platform.isLinux) {
        final linuxInfo = await _deviceInfoPlugin.linuxInfo;
        return 'linux_${linuxInfo.machineId ?? 'unknown'}';
      }

      return 'unknown_desktop_mac';
    } catch (e) {
      print('Error getting desktop MAC address: $e');
      return 'error_desktop_mac';
    }
  }

  // Format MAC address to standard format (XX:XX:XX:XX:XX:XX)
  String _formatMacAddress(String macAddress) {
    // Remove any existing separators and convert to uppercase
    String cleanMac = macAddress.replaceAll(RegExp(r'[:-]'), '').toUpperCase();

    // Ensure it's 12 characters long
    if (cleanMac.length != 12) {
      return macAddress; // Return original if not standard length
    }

    // Format as XX:XX:XX:XX:XX:XX
    return '${cleanMac.substring(0, 2)}:${cleanMac.substring(2, 4)}:${cleanMac.substring(4, 6)}:${cleanMac.substring(6, 8)}:${cleanMac.substring(8, 10)}:${cleanMac.substring(10, 12)}';
  }

  // Get Android device ID
  Future<String> _getAndroidDeviceId() async {
    final AndroidDeviceInfo androidInfo = await _deviceInfoPlugin.androidInfo;
    return androidInfo.id;
  }

  // Get iOS device ID
  Future<String> _getIosDeviceId() async {
    final IosDeviceInfo iosInfo = await _deviceInfoPlugin.iosInfo;
    return iosInfo.identifierForVendor ?? 'unknown_ios_device';
  }

  // Get macOS device ID
  Future<String> _getMacOsDeviceId() async {
    final MacOsDeviceInfo macOsInfo = await _deviceInfoPlugin.macOsInfo;
    return macOsInfo.systemGUID ?? 'unknown_macos_device';
  }

  // Get Windows device ID
  Future<String> _getWindowsDeviceId() async {
    final WindowsDeviceInfo windowsInfo = await _deviceInfoPlugin.windowsInfo;
    return windowsInfo.deviceId;
  }

  // Get Linux device ID
  Future<String> _getLinuxDeviceId() async {
    final LinuxDeviceInfo linuxInfo = await _deviceInfoPlugin.linuxInfo;
    return linuxInfo.machineId ?? 'unknown_linux_device';
  }
}
