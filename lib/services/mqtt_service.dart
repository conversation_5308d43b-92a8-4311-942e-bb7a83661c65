import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/mqtt_message_model.dart';
import 'settings_service.dart';

enum MqttConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
}

class MqttService {
  MqttServerClient? _client;
  final SettingsService _settingsService = SettingsService();

  // Stream controllers
  final StreamController<MqttConnectionState> _connectionStateController =
      StreamController<MqttConnectionState>.broadcast();
  final StreamController<MqttMessageModel> _messageController =
      StreamController<MqttMessageModel>.broadcast();

  // Streams
  Stream<MqttConnectionState> get connectionState =>
      _connectionStateController.stream;
  Stream<MqttMessageModel> get messages => _messageController.stream;

  // Current connection state
  MqttConnectionState _currentState = MqttConnectionState.disconnected;
  MqttConnectionState get currentState => _currentState;
  Timer? _reconnectTimer; // Timer for automatic reconnection
  int _reconnectAttempts = 0; // Track number of reconnection attempts
  static const int _maxReconnectAttempts =
      10; // Maximum number of reconnection attempts

  // Check network connectivity
  Future<bool> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        print('No network connectivity');
        return false;
      }
      return true;
    } catch (e) {
      print('Error checking connectivity: $e');
      return true; // Assume connectivity if we can't check
    }
  }

  // Check DNS resolution for a hostname
  Future<bool> _checkDnsResolution(String host) async {
    try {
      print('Checking DNS resolution for: $host');
      final List<InternetAddress> addresses = await InternetAddress.lookup(
        host,
      );
      if (addresses.isNotEmpty) {
        print('DNS resolution successful: ${addresses.first.address}');
        return true;
      }
      print('DNS resolution failed: No addresses found');
      return false;
    } on SocketException catch (e) {
      print('DNS resolution failed: $e');
      return false;
    } catch (e) {
      print('Error checking DNS resolution: $e');
      return false;
    }
  }

  // Connect to MQTT server
  Future<bool> connect() async {
    // Check if already connected
    if (_currentState == MqttConnectionState.connected) {
      return true;
    }

    // Check network connectivity
    final hasConnectivity = await _checkConnectivity();
    if (!hasConnectivity) {
      print('No network connectivity detected');
      _updateConnectionState(MqttConnectionState.error);
      return false;
    }

    try {
      // Load settings
      final settings = await _settingsService.loadSettings();
      if (settings?.mqttServerAddress == null) {
        _updateConnectionState(MqttConnectionState.error);
        return false;
      }

      // Use server address directly without protocol
      final server = settings!.mqttServerAddress!;
      print('Connecting to MQTT server: $server'); // Debug log

      // Check DNS resolution
      final hasDnsResolution = await _checkDnsResolution(server);
      if (!hasDnsResolution) {
        print('DNS resolution failed for: $server');
        _updateConnectionState(MqttConnectionState.error);
        return false;
      }

      // Use port from settings or default to 1883
      final port = int.tryParse(settings.mqttPort ?? '1883') ?? 1883;
      print('Using MQTT port: $port'); // Debug log

      // Create client
      _client = MqttServerClient(server, settings.deviceId ?? 'esop_client');
      _client!.port = port;
      _client!.keepAlivePeriod = 60;
      _client!.onDisconnected = _onDisconnected;
      _client!.onConnected = _onConnected;
      _client!.onSubscribed = _onSubscribed;

      // Set logging
      _client!.logging(on: true);

      // Set connection message timeout
      _client!.connectTimeoutPeriod = 5000; // 5 seconds

      // Set connection message using MAC address as client identifier
      final clientId = settings.getDeviceIdentifier() ?? 'esop_client';
      final connMess = MqttConnectMessage()
          .withClientIdentifier(clientId)
          .withWillTopic('willtopic')
          .withWillMessage('Will message')
          .startClean()
          .withWillQos(MqttQos.atLeastOnce);
      _client!.connectionMessage = connMess;

      // Update connection state
      _updateConnectionState(MqttConnectionState.connecting);

      // Connect to server
      await _client!.connect();

      // Subscribe to topic (use configured topic or default to esopChannel)
      final topic = settings.mqttTopic ?? 'esopChannel';
      _client!.subscribe(topic, MqttQos.atLeastOnce);

      // Listen for messages
      _client!.updates!.listen(_onMessage);

      return true;
    } catch (e) {
      print('Error connecting to MQTT server: $e');
      _updateConnectionState(MqttConnectionState.error);
      // Explicitly trigger disconnection to start reconnection
      _onDisconnected();
      return false;
    }
  }

  // Disconnect from MQTT server
  Future<void> disconnect() async {
    try {
      _updateConnectionState(MqttConnectionState.disconnecting);
      _client?.disconnect();
    } catch (e) {
      print('Error disconnecting from MQTT server: $e');
    }
  }

  // Handle disconnection
  void _onDisconnected() {
    _updateConnectionState(MqttConnectionState.disconnected);
    _client = null;

    // Start reconnection timer if not already running and under max attempts
    if ((_reconnectTimer == null || !_reconnectTimer!.isActive) &&
        _reconnectAttempts < _maxReconnectAttempts) {
      // Calculate delay with exponential backoff (10s, 20s, 40s... max 5min)
      final delaySeconds = min(10 * pow(2, _reconnectAttempts).toInt(), 300);
      _reconnectAttempts++;

      print(
        'MQTT disconnected, attempt $_reconnectAttempts/$_maxReconnectAttempts, '
        'scheduling reconnection in ${delaySeconds}s',
      );

      _reconnectTimer = Timer(Duration(seconds: delaySeconds), () async {
        if (_currentState == MqttConnectionState.disconnected) {
          print(
            'Attempting MQTT reconnection (attempt $_reconnectAttempts)...',
          );
          await connect();
        }
      });
    } else if (_reconnectAttempts >= _maxReconnectAttempts) {
      print(
        'Warning: Maximum reconnection attempts ($_maxReconnectAttempts) reached. '
        'Automatic reconnection disabled.',
      );
    }
  }

  // Handle connection
  void _onConnected() {
    _updateConnectionState(MqttConnectionState.connected);

    // Cancel any pending reconnection timer
    if (_reconnectTimer != null && _reconnectTimer!.isActive) {
      print('MQTT connected, canceling reconnection timer');
      _reconnectTimer!.cancel();
      _reconnectTimer = null;
    }

    // Reset reconnection attempts counter
    if (_reconnectAttempts > 0) {
      print('MQTT connection restored after $_reconnectAttempts attempts');
      _reconnectAttempts = 0;
    }
  }

  // Handle subscription
  void _onSubscribed(String topic) {
    print('Subscribed to topic: $topic');
  }

  // Handle message
  void _onMessage(List<MqttReceivedMessage<MqttMessage>> messages) {
    for (var message in messages) {
      final recMess = message.payload as MqttPublishMessage;
      final payload = MqttPublishPayload.bytesToStringAsString(
        recMess.payload.message,
      );

      print('Received MQTT message: $payload');

      try {
        final jsonData = jsonDecode(payload);

        // Validate message format
        if (jsonData['type'] == null) {
          print('Invalid MQTT message: missing required type field');
          continue;
        }

        // Validate type value (1, 2 or 3)
        final type = jsonData['type'];
        if (type != 1 && type != 2 && type != 3) {
          print('Invalid MQTT message type: $type (must be 1, 2 or 3)');
          continue;
        }

        if (jsonData['group_name'] == null) {
          print('Invalid MQTT message: missing required group_name field');
          continue;
        }

        if (jsonData['list'] == null || jsonData['list'] is! List) {
          print('Invalid MQTT message: missing or invalid list field');
          continue;
        }

        // Validate each item in the list
        for (var item in jsonData['list']) {
          if (item['download_file'] == null) {
            print('Invalid MQTT message item: missing download_file field');
            continue;
          }
          if (item['equipment_alias_name'] == null) {
            print(
              'Invalid MQTT message item: missing equipment_alias_name field',
            );
            continue;
          }
        }

        final mqttMessage = MqttMessageModel.fromJson(jsonData);
        _messageController.add(mqttMessage);
      } catch (e) {
        print('Error parsing MQTT message: $e');
      }
    }
  }

  // Update connection state
  void _updateConnectionState(MqttConnectionState state) {
    _currentState = state;
    _connectionStateController.add(state);
  }

  // Dispose resources
  void dispose() {
    // Cancel reconnection timer if active
    if (_reconnectTimer != null && _reconnectTimer!.isActive) {
      print('Disposing MQTT service, canceling reconnection timer');
      _reconnectTimer!.cancel();
      _reconnectTimer = null;
    }

    _connectionStateController.close();
    _messageController.close();
    disconnect();
  }
}
