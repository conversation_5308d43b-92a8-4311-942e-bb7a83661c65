import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:io';

class ApiService {
  // Register device with the server
  Future<bool> registerDevice({
    required String serverAddress,
    required String serverPort,
    required String deviceAlias,
    required String macAddress, // Changed from deviceId to macAddress
    required String groupName,
    required String aliasName,
  }) async {
    // If server address is empty, don't make the request
    if (serverAddress.isEmpty) {
      debugPrint('Server address is empty, skipping device registration');
      return false;
    }

    try {
      // Check for internet connectivity first
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        debugPrint('No internet connection, skipping device registration');
        return false;
      }

      // Get device IP address
      String ipAddress = await _getDeviceIpAddress() ?? '';

      // Prepare the API endpoint URL
      // Make sure to handle both http:// prefixed and non-prefixed addresses
      String baseUrl = serverAddress;
      String port = serverPort;
      if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
        baseUrl = 'http://$baseUrl:$port';
      }

      final url = Uri.parse('$baseUrl/v1/equipment/addEquipment');

      // Prepare the request body
      final body = {
        'name': deviceAlias,
        'device_id': macAddress, // Using MAC address as device identifier
        'group_name': groupName,
        'alias_name': aliasName,
        'ip': ipAddress,
      };

      debugPrint('Registering device with server: $url');
      debugPrint('Request body: $body');

      // Make the POST request
      final response = await http
          .post(
            url,
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(body),
          )
          .timeout(
            const Duration(seconds: 10),
          ); // Add timeout to prevent hanging

      // Check the response
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        debugPrint('Device registration successful: ${response.body}');
        return true;
      } else {
        debugPrint(
          'Device registration failed: ${response.statusCode}, ${response.body}',
        );
        return false;
      }
    } catch (e) {
      debugPrint('Error registering device: $e');
      return false;
    }
  }

  // Helper method to get the device's IP address
  Future<String?> _getDeviceIpAddress() async {
    try {
      // Get list of network interfaces
      final interfaces = await NetworkInterface.list(
        includeLoopback: false,
        type: InternetAddressType.IPv4,
      );

      // Find a non-loopback IPv4 address
      for (var interface in interfaces) {
        for (var addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4) {
            return addr.address;
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting device IP address: $e');
      return null;
    }
  }
}
