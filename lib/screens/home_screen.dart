import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/mqtt_provider.dart';
import '../providers/file_provider.dart';
import '../providers/settings_provider.dart';
import '../models/mqtt_message_model.dart';
import '../services/api_service.dart';
import '../services/life_signal_service.dart';
import '../services/orientation_service.dart';
import 'settings_screen.dart';
import 'webview_screen.dart';
import 'pdf_viewer_screen.dart';
import '../utils/gesture_detector.dart';
import '../utils/file_utils.dart';
import '../l10n/app_localizations_extension.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ApiService _apiService = ApiService();
  final LifeSignalService _lifeSignalService = LifeSignalService();
  bool _ignoreInitialMessages = true;

  @override
  void initState() {
    super.initState();

    // Ensure fullscreen mode is maintained
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );

    // Initialize app with required checks
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkRequiredSettings();
      _applyOrientationSetting();
    });
  }

  // Apply orientation setting from settings
  Future<void> _applyOrientationSetting() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final orientation =
        settingsProvider.settings.screenOrientation ?? 'landscape';
    await OrientationService.applyOrientation(orientation);
  }

  // Register device with the server
  Future<void> _registerDeviceWithServer() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final settings = settingsProvider.settings;

    // Get required parameters
    final serverAddress = settings.mqttServerAddress ?? '';
    final serverPort = settings.serverPort ?? '';
    final deviceAlias = settings.deviceAlias ?? '';
    final deviceId = settings.deviceId ?? '';
    final groupName = settings.groupName ?? '';
    final aliasName = settings.deviceAlias ?? '';

    // Skip if server address is empty
    if (serverAddress.isEmpty) {
      debugPrint('Server address is empty, skipping device registration');
      return;
    }

    // Skip if any required parameter is missing
    if (deviceAlias.isEmpty || deviceId.isEmpty || groupName.isEmpty) {
      debugPrint('Required parameters missing, skipping device registration');
      return;
    }

    // Register device with the server
    try {
      final success = await _apiService.registerDevice(
        serverAddress: serverAddress,
        serverPort: serverPort,
        deviceAlias: deviceAlias,
        deviceId: deviceId,
        aliasName: aliasName,
        groupName: groupName,
      );

      if (success) {
        debugPrint('Device registered successfully with the server');

        // Start life signal after successful registration
        _lifeSignalService.startLifeSignal(settingsProvider: settingsProvider);
      } else {
        debugPrint('Failed to register device with the server');
      }
    } catch (e) {
      debugPrint('Error registering device with the server: $e');
    }
  }

  @override
  void dispose() {
    // Stop life signal when screen is disposed
    _lifeSignalService.stopLifeSignal();
    super.dispose();
  }

  // Check if required settings are filled
  Future<void> _checkRequiredSettings() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final mqttProvider = Provider.of<MqttProvider>(context, listen: false);
    final fileProvider = Provider.of<FileProvider>(context, listen: false);

    // Wait for settings to be loaded if they're still loading
    if (settingsProvider.isLoading) {
      debugPrint('Settings are still loading, waiting...');
      // Wait a bit and check again
      await Future.delayed(const Duration(milliseconds: 500));
      if (mounted) {
        _checkRequiredSettings();
      }
      return;
    }

    // Make sure settings are initialized
    if (settingsProvider.settings.deviceId == null ||
        settingsProvider.settings.deviceId!.isEmpty) {
      debugPrint('Settings not fully initialized, initializing...');
      await settingsProvider.initSettings();
    }

    // Check if server address, group name, and device alias are filled
    final settings = settingsProvider.settings;
    final serverAddress = settings.mqttServerAddress;
    final groupName = settings.groupName;
    final deviceAlias = settings.deviceAlias;

    bool settingsComplete = true;

    // Check if any required setting is missing
    if (serverAddress == null || serverAddress.isEmpty) {
      debugPrint('Server address is missing');
      settingsComplete = false;
    }

    if (groupName == null || groupName.isEmpty) {
      debugPrint('Group name is missing');
      settingsComplete = false;
    }

    if (deviceAlias == null || deviceAlias.isEmpty) {
      debugPrint('Device alias is missing');
      settingsComplete = false;
    }

    if (!settingsComplete && mounted) {
      // Navigate to settings page if any required setting is missing
      debugPrint(
        'Navigating to settings page due to missing required settings',
      );
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const SettingsScreen()),
      );

      // After returning from settings, check again
      _checkRequiredSettings();
      return;
    }

    // If all settings are complete, register device and check for existing files
    if (settingsComplete) {
      debugPrint(
        'All required settings are complete, registering device with server',
      );

      // Register device with the server
      await _registerDeviceWithServer();

      debugPrint('Checking for existing files');
      final hasExistingFile = await fileProvider.findAndUseExistingFile();

      if (hasExistingFile && mounted && fileProvider.indexHtmlFile != null) {
        // If existing file found, open it in WebView
        debugPrint('Opening existing file in WebView');

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                WebViewScreen(htmlFile: fileProvider.indexHtmlFile!),
          ),
        );
      }

      // Initialize MQTT provider
      mqttProvider.initialize();

      // Listen for MQTT messages
      mqttProvider.listenForMessages(_handleMqttMessage);

      // Set a timer to start accepting MQTT messages after a delay
      // This prevents processing retained messages immediately on startup
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _ignoreInitialMessages = false;
          });
          debugPrint('Now accepting MQTT messages after startup delay');
        }
      });
    }
  }

  // Handle MQTT message
  void _handleMqttMessage(MqttMessageModel message) async {
    // Ignore messages during initial startup period to avoid processing retained messages
    if (_ignoreInitialMessages) {
      debugPrint('Ignoring MQTT message during startup period');
      return;
    }

    // Check if message has file list
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('No files to process in MQTT message');
      return;
    }

    final fileProvider = Provider.of<FileProvider>(context, listen: false);
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final deviceAlias = settingsProvider.settings.deviceAlias ?? '';

    // Process message based on type
    switch (message.type) {
      case 1: // Partial send
        await _processPartialMessage(message, deviceAlias, fileProvider);
        break;
      case 2: // All send
        await _processAllMessage(message, fileProvider);
        break;
      case 3: // Rule-based send
        await _processRuleBasedMessage(message, deviceAlias, fileProvider);
        break;
      default:
        debugPrint('Unknown message type: ${message.type}');
    }
  }

  // Process partial message (type 1)
  Future<void> _processPartialMessage(
    MqttMessageModel message,
    String deviceAlias,
    FileProvider fileProvider,
  ) async {
    // Get group name from settings
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final groupName = settingsProvider.settings.groupName ?? '';
    // 添加空值检查
    final msgGroupName = message.groupName ?? '';
    // Check group name match
    if (msgGroupName != '*' && !msgGroupName.contains(groupName)) {
      debugPrint(
        'Group name does not match: ${message.groupName} != $groupName',
      );
      return;
    }

    // Check if file list exists and is not empty
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('No files to process in message');
      return;
    }

    // Process each file in the list
    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('Skipping file item with empty download URL');
        continue;
      }

      if (fileItem.equipmentAliasName == '*' ||
          fileItem.equipmentAliasName == deviceAlias) {
        await _downloadAndProcessFile(fileItem.downloadFile!, fileProvider);
      }
    }
  }

  // Process all message (type 2)
  Future<void> _processAllMessage(
    MqttMessageModel message,
    FileProvider fileProvider,
  ) async {
    // Check if file list exists and is not empty
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('No files to process in message');
      return;
    }

    // Process all files in the list
    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('Skipping file item with empty download URL');
        continue;
      }

      await _downloadAndProcessFile(fileItem.downloadFile!, fileProvider);
    }
  }

  // Process rule-based message (type 3)
  Future<void> _processRuleBasedMessage(
    MqttMessageModel message,
    String deviceAlias,
    FileProvider fileProvider,
  ) async {
    // Get group name from settings
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final groupName = settingsProvider.settings.groupName ?? '';
    // 添加空值检查
    final msgGroupName = message.groupName ?? '';
    // Check group name match
    if (msgGroupName != '*' && !msgGroupName.contains(groupName)) {
      debugPrint(
        'Group name does not match: ${message.groupName} != $groupName',
      );
      return;
    }

    // Check if file list exists and is not empty
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('No files to process in message');
      return;
    }

    // Process each file in the list based on equipment alias
    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('Skipping file item with empty download URL');
        continue;
      }

      if (fileItem.equipmentAliasName == '*' ||
          fileItem.equipmentAliasName == deviceAlias) {
        await _downloadAndProcessFile(fileItem.downloadFile!, fileProvider);
      }
    }
  }

  // Download and process file
  Future<void> _downloadAndProcessFile(
    String fileUrl,
    FileProvider fileProvider,
  ) async {
    if (fileUrl.isEmpty) {
      debugPrint('Empty file URL');
      return;
    }

    // Get server address from settings
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final serverAddress = settingsProvider.settings.mqttServerAddress ?? '';
    final serverPort = settingsProvider.settings.serverPort ?? '';
    // Check if URL starts with http or https
    String fullUrl = fileUrl;
    if (!fileUrl.toLowerCase().startsWith('http')) {
      // URL doesn't start with http, add server address as prefix
      if (serverAddress.isEmpty) {
        debugPrint('Server address is empty, cannot prepend to URL: $fileUrl');
        return;
      }

      // Ensure server address has http:// prefix
      String prefix = serverAddress.toLowerCase().startsWith('http')
          ? serverAddress
          : 'http://$serverAddress:$serverPort';

      // Ensure path starts with / if needed
      if (!fileUrl.startsWith('/') && !prefix.endsWith('/')) {
        fullUrl = '$prefix/$fileUrl';
      } else {
        fullUrl = '$prefix$fileUrl';
      }

      debugPrint('Modified URL: $fullUrl (original: $fileUrl)');
    } else {
      debugPrint('Using original URL: $fileUrl');
    }

    // Process file from URL
    final success = await fileProvider.processFileFromUrl(fullUrl);

    if (success && mounted) {
      // Check if this is a document file or ZIP file
      if (FileUtils.isDocumentFile(fullUrl)) {
        // Check if this is a PDF file - navigate to PDF viewer
        if (FileUtils.isPdfFile(fullUrl) &&
            fileProvider.downloadedFile != null) {
          debugPrint('Navigating to PDF viewer');
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  PdfViewerScreen(pdfFile: fileProvider.downloadedFile!),
            ),
          );
        } else {
          // Non-PDF document files are opened directly by the system
          debugPrint('Document file opened successfully');
        }
      } else if (FileUtils.isZipFile(fullUrl) &&
          fileProvider.indexHtmlFile != null) {
        // Navigate to WebView screen for ZIP files

        //判断file_type 是html类型还是pdf类型

        print("Navigate to WebView screen for ZIP files");
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                WebViewScreen(htmlFile: fileProvider.indexHtmlFile!),
          ),
        );
      }
    }
  }

  // Navigate to settings screen
  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.appTitle),
        actions: [
          // MQTT Status Indicator
          Consumer<MqttProvider>(
            builder: (context, mqttProvider, child) {
              return Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Text(
                      mqttProvider.isConnected
                          ? context.l10n.connected
                          : context.l10n.disconnected,
                      style: TextStyle(
                        color: mqttProvider.isConnected
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      mqttProvider.isConnected
                          ? Icons.cloud_done
                          : Icons.cloud_off,
                      color: mqttProvider.isConnected
                          ? Colors.green
                          : Colors.red,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: MultiTouchGestureDetector(
        onDoubleTapWithTwoFingers: _navigateToSettings,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                context.l10n.appTitle,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                context.l10n.doubleTapWithTwoFingers,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // MQTT Status
              Consumer<MqttProvider>(
                builder: (context, mqttProvider, child) {
                  return Column(
                    children: [
                      Text(
                        context.l10n.mqttStatus(
                          mqttProvider.connectionState
                              .toString()
                              .split('.')
                              .last,
                        ),
                        style: TextStyle(
                          color: mqttProvider.isConnected
                              ? Colors.green
                              : Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (!mqttProvider.isConnected)
                        ElevatedButton(
                          onPressed: mqttProvider.connect,
                          child: Text(context.l10n.connect),
                        ),
                      if (mqttProvider.error.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            mqttProvider.error,
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                        ),
                    ],
                  );
                },
              ),

              const SizedBox(height: 32),

              // File Operation Status
              Consumer<FileProvider>(
                builder: (context, fileProvider, child) {
                  if (fileProvider.state == FileOperationState.idle) {
                    return const SizedBox.shrink();
                  }

                  return Column(
                    children: [
                      Text(
                        context.l10n.fileOperation(
                          fileProvider.state.toString().split('.').last,
                        ),
                        style: TextStyle(
                          color: fileProvider.state == FileOperationState.error
                              ? Colors.red
                              : Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (fileProvider.state == FileOperationState.checking)
                        const CircularProgressIndicator(),
                      if (fileProvider.state == FileOperationState.downloading)
                        Column(
                          children: [
                            const SizedBox(height: 8),
                            // Show download progress
                            LinearProgressIndicator(
                              value: fileProvider.downloadProgress,
                              backgroundColor: Colors.grey[300],
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                Colors.blue,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${(fileProvider.downloadProgress * 100).toStringAsFixed(1)}%',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      if (fileProvider.state == FileOperationState.extracting)
                        const CircularProgressIndicator(),
                      if (fileProvider.state ==
                          FileOperationState.openingDocument)
                        Column(
                          children: [
                            const SizedBox(height: 8),
                            const CircularProgressIndicator(),
                            const SizedBox(height: 8),
                            Text(
                              context.l10n.openingDocument,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      if (fileProvider.state ==
                          FileOperationState.usingExisting)
                        Column(
                          children: [
                            const SizedBox(height: 8),
                            Text(
                              context.l10n.usingExistingFile,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.green,
                              ),
                            ),
                            const SizedBox(height: 4),
                            LinearProgressIndicator(
                              value: 1.0, // 100% progress
                              backgroundColor: Colors.grey[300],
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                Colors.green,
                              ),
                            ),
                          ],
                        ),
                      if (fileProvider.error.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            fileProvider.error,
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                        ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
