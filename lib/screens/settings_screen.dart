import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/localization_provider.dart';
import '../l10n/app_localizations_extension.dart';
import '../services/orientation_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mqttServerController = TextEditingController();
  final _apiServerController = TextEditingController();
  final _mqttPortController = TextEditingController(); // Added for MQTT port
  final _mqttTopicController = TextEditingController(); // Added for MQTT topic
  final _groupNameController = TextEditingController(); // Added for group name
  final _deviceAliasController = TextEditingController();
  String _selectedLanguage = 'zh'; // Default to Chinese
  String _selectedOrientation = 'landscape'; // Default to landscape

  @override
  void initState() {
    super.initState();

    // Ensure fullscreen mode is maintained
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );

    // Initialize controllers with current settings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );
      _mqttServerController.text =
          settingsProvider.settings.mqttServerAddress ?? '';
      _apiServerController.text =
          settingsProvider.settings.serverPort ?? '8090'; // Default port
      _mqttPortController.text =
          settingsProvider.settings.mqttPort ?? '1883'; // Default port
      _mqttTopicController.text =
          settingsProvider.settings.mqttTopic ?? 'esopChannel'; // Default topic
      _groupNameController.text =
          settingsProvider.settings.groupName ?? ''; // Group name
      _deviceAliasController.text = settingsProvider.settings.deviceAlias ?? '';

      // Make sure to update the UI when setting the language and orientation
      setState(() {
        _selectedLanguage = settingsProvider.settings.languageCode ?? 'zh';
        _selectedOrientation =
            settingsProvider.settings.screenOrientation ?? 'landscape';
        print('Initialized language to: $_selectedLanguage'); // Debug log
        print('Initialized orientation to: $_selectedOrientation'); // Debug log
      });
    });
  }

  @override
  void dispose() {
    _mqttServerController.dispose();
    _apiServerController.dispose(); // Dispose API port controller
    _mqttPortController.dispose(); // Dispose MQTT port controller
    _mqttTopicController.dispose(); // Dispose MQTT topic controller
    _groupNameController.dispose(); // Dispose group name controller
    _deviceAliasController.dispose();
    super.dispose();
  }

  // Save settings
  Future<void> _saveSettings() async {
    if (_formKey.currentState!.validate()) {
      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );
      final localizationProvider = Provider.of<LocalizationProvider>(
        context,
        listen: false,
      );

      // Update MQTT server address
      if (_mqttServerController.text !=
          settingsProvider.settings.mqttServerAddress) {
        await settingsProvider.updateMqttServerAddress(
          _mqttServerController.text,
        );
      }

      // Update MQTT port
      if (_mqttPortController.text != settingsProvider.settings.mqttPort) {
        await settingsProvider.updateMqttPort(_mqttPortController.text);
      }

      // Update MQTT topic
      if (_mqttTopicController.text != settingsProvider.settings.mqttTopic) {
        await settingsProvider.updateMqttTopic(_mqttTopicController.text);
      }

      // Update group name
      if (_groupNameController.text != settingsProvider.settings.groupName) {
        await settingsProvider.updateGroupName(_groupNameController.text);
      }

      // Update device alias
      if (_deviceAliasController.text !=
          settingsProvider.settings.deviceAlias) {
        await settingsProvider.updateDeviceAlias(_deviceAliasController.text);
      }

      // Update language
      if (_selectedLanguage != settingsProvider.settings.languageCode) {
        print(
          'Changing language from ${settingsProvider.settings.languageCode} to $_selectedLanguage',
        ); // Debug log
        await settingsProvider.updateLanguageCode(_selectedLanguage);
        await localizationProvider.changeLocale(Locale(_selectedLanguage));
        print(
          'Language changed to: ${localizationProvider.locale.languageCode}',
        ); // Debug log
      }

      // Update screen orientation
      if (_selectedOrientation != settingsProvider.settings.screenOrientation) {
        print(
          'Changing orientation from ${settingsProvider.settings.screenOrientation} to $_selectedOrientation',
        ); // Debug log
        await settingsProvider.updateScreenOrientation(_selectedOrientation);
        // Apply the new orientation immediately
        await OrientationService.applyOrientation(_selectedOrientation);
      }

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(context.l10n.settingsSaved)));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(context.l10n.settings)),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Language Selection
                    Text(
                      context.l10n.language,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        // English option
                        Expanded(
                          child: RadioListTile<String>(
                            title: Text(context.l10n.english),
                            value: 'en',
                            groupValue: _selectedLanguage,
                            onChanged: (value) {
                              setState(() {
                                _selectedLanguage = value!;
                              });
                            },
                          ),
                        ),
                        // Chinese option
                        Expanded(
                          child: RadioListTile<String>(
                            title: Text(context.l10n.chinese),
                            value: 'zh',
                            groupValue: _selectedLanguage,
                            onChanged: (value) {
                              setState(() {
                                _selectedLanguage = value!;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Screen Orientation Selection
                    Text(
                      context.l10n.screenOrientation,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        // Landscape option
                        Expanded(
                          child: RadioListTile<String>(
                            title: Text(context.l10n.landscapeDisplay),
                            value: 'landscape',
                            groupValue: _selectedOrientation,
                            onChanged: (value) {
                              setState(() {
                                _selectedOrientation = value!;
                              });
                            },
                          ),
                        ),
                        // Portrait option
                        Expanded(
                          child: RadioListTile<String>(
                            title: Text(context.l10n.portraitDisplay),
                            value: 'portrait',
                            groupValue: _selectedOrientation,
                            onChanged: (value) {
                              setState(() {
                                _selectedOrientation = value!;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // MQTT Server Address
                    TextFormField(
                      controller: _mqttServerController,
                      decoration: InputDecoration(
                        labelText: context.l10n.mqttServerAddress,
                        hintText: context.l10n.mqttServerAddressHint,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterMqttServerAddress;
                        }
                        // No protocol validation needed
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // MQTT Port
                    TextFormField(
                      controller: _apiServerController,
                      decoration: InputDecoration(
                        labelText: context.l10n.apiServerPort,
                        hintText: context.l10n.apiServerPortHint,
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterMqttPort;
                        }

                        // Validate port number
                        try {
                          final port = int.parse(value);
                          if (port <= 0 || port > 65535) {
                            return context.l10n.portNumberMustBeBetween;
                          }
                        } catch (e) {
                          return context.l10n.pleaseEnterValidPort;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // MQTT Port
                    TextFormField(
                      controller: _mqttPortController,
                      decoration: InputDecoration(
                        labelText: context.l10n.mqttPort,
                        hintText: context.l10n.mqttPortHint,
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterMqttPort;
                        }

                        // Validate port number
                        try {
                          final port = int.parse(value);
                          if (port <= 0 || port > 65535) {
                            return context.l10n.portNumberMustBeBetween;
                          }
                        } catch (e) {
                          return context.l10n.pleaseEnterValidPort;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // MQTT Topic
                    // TextFormField(
                    //   controller: _mqttTopicController,
                    //   decoration: InputDecoration(
                    //     labelText: context.l10n.mqttTopic,
                    //     hintText: context.l10n.mqttTopicHint,
                    //   ),
                    //   validator: (value) {
                    //     if (value == null || value.isEmpty) {
                    //       return context.l10n.pleaseEnterMqttTopic;
                    //     }
                    //     return null;
                    //   },
                    // ),
                    // const SizedBox(height: 16),

                    // Group Name
                    TextFormField(
                      controller: _groupNameController,
                      decoration: InputDecoration(
                        labelText: context.l10n.groupName,
                        hintText: context.l10n.groupNameHint,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterGroupName;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Device Alias
                    TextFormField(
                      controller: _deviceAliasController,
                      decoration: InputDecoration(
                        labelText: context.l10n.deviceAlias,
                        hintText: context.l10n.deviceAliasHint,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterDeviceAlias;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Device ID (read-only)
                    TextFormField(
                      initialValue:
                          settingsProvider.settings.deviceId ??
                          context.l10n.loading,
                      decoration: InputDecoration(
                        labelText: context.l10n.deviceId,
                      ),
                      readOnly: true,
                    ),
                    const SizedBox(height: 24),

                    // Save Button
                    Center(
                      child: ElevatedButton(
                        onPressed: settingsProvider.isLoading
                            ? null
                            : _saveSettings,
                        child: settingsProvider.isLoading
                            ? const CircularProgressIndicator()
                            : Text(context.l10n.saveSettings),
                      ),
                    ),

                    // Error Message
                    if (settingsProvider.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          settingsProvider.error,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
