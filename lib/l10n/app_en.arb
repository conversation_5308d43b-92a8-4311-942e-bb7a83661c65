{"appTitle": "ESOP Client", "settings": "Settings", "language": "Language", "english": "English", "chinese": "Chinese", "settingsSaved": "Setting<PERSON> saved", "mqttServerAddress": "Server Address", "mqttServerAddressHint": "e.g., example.com or ip addr", "pleaseEnterMqttServerAddress": "Please enter server address", "apiServerPort": "Api Port", "apiServerPortHint": "e.g., 8090", "mqttPort": "MQTT Port", "mqttPortHint": "e.g., 1883", "pleaseEnterMqttPort": "Please enter MQTT port", "portNumberMustBeBetween": "Port number must be between 1-65535", "pleaseEnterValidPort": "Please enter a valid port number", "mqttTopic": "MQTT Topic", "mqttTopicHint": "e.g., esopChannel", "pleaseEnterMqttTopic": "Please enter MQTT topic", "groupName": "Group Name", "groupNameHint": "e.g., Production Line 1", "pleaseEnterGroupName": "Please enter group name", "deviceAlias": "<PERSON><PERSON>", "deviceAliasHint": "e.g., <PERSON><PERSON>", "pleaseEnterDeviceAlias": "Please enter device alias", "deviceId": "Device ID", "loading": "Loading...", "saveSettings": "Save Settings", "connected": "Connected", "disconnected": "Disconnected", "mqttStatus": "MQTT Status: {status}", "@mqttStatus": {"placeholders": {"status": {"type": "String"}}}, "connect": "Connect", "doubleTapWithTwoFingers": "Double tap with two fingers to access settings", "fileOperation": "File Operation: {status}", "@fileOperation": {"placeholders": {"status": {"type": "String"}}}, "retry": "Retry", "usingExistingFile": "Using existing file", "checking": "Checking", "openingDocument": "Opening document...", "errorLoadingContent": "Error loading content: {message}", "@errorLoadingContent": {"placeholders": {"message": {"type": "String"}}}, "httpError": "HTTP Error: {statusCode} {description}", "@httpError": {"placeholders": {"statusCode": {"type": "int"}, "description": {"type": "String"}}}, "error": "Error", "back": "Back", "screenOrientation": "Screen Orientation", "landscapeDisplay": "Landscape Display", "portraitDisplay": "Portrait Display"}