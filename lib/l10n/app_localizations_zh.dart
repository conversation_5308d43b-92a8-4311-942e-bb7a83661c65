// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'ESOP 客户端';

  @override
  String get settings => '设置';

  @override
  String get language => '语言';

  @override
  String get english => '英文';

  @override
  String get chinese => '中文';

  @override
  String get settingsSaved => '设置已保存';

  @override
  String get mqttServerAddress => '服务器地址';

  @override
  String get mqttServerAddressHint => '例如：example.com 或 ***********';

  @override
  String get pleaseEnterMqttServerAddress => '请输入 服务器地址';

  @override
  String get apiServerPort => 'Api 端口';

  @override
  String get apiServerPortHint => 'e.g., 8090';

  @override
  String get mqttPort => 'MQTT 端口';

  @override
  String get mqttPortHint => '例如：1883';

  @override
  String get pleaseEnterMqttPort => '请输入 MQTT 端口';

  @override
  String get portNumberMustBeBetween => '端口号必须在 1-65535 之间';

  @override
  String get pleaseEnterValidPort => '请输入有效的端口号';

  @override
  String get mqttTopic => 'MQTT 主题';

  @override
  String get mqttTopicHint => '例如：esopChannel';

  @override
  String get pleaseEnterMqttTopic => '请输入 MQTT 主题';

  @override
  String get groupName => '分组名称';

  @override
  String get groupNameHint => '例如：生产线 1';

  @override
  String get pleaseEnterGroupName => '请输入分组名称';

  @override
  String get deviceAlias => '设备别名';

  @override
  String get deviceAliasHint => '例如：我的设备';

  @override
  String get pleaseEnterDeviceAlias => '请输入设备别名';

  @override
  String get deviceId => '设备 ID';

  @override
  String get loading => '加载中...';

  @override
  String get saveSettings => '保存设置';

  @override
  String get connected => '已连接';

  @override
  String get disconnected => '未连接';

  @override
  String mqttStatus(String status) {
    return 'MQTT 状态: $status';
  }

  @override
  String get connect => '连接';

  @override
  String get doubleTapWithTwoFingers => '双指双击以访问设置';

  @override
  String fileOperation(String status) {
    return '文件操作: $status';
  }

  @override
  String get retry => '重试';

  @override
  String get usingExistingFile => '使用已存在的文件';

  @override
  String get checking => '检查中';

  @override
  String get openingDocument => '正在打开文档...';

  @override
  String errorLoadingContent(String message) {
    return '加载内容错误: $message';
  }

  @override
  String httpError(int statusCode, String description) {
    return 'HTTP 错误: $statusCode $description';
  }

  @override
  String get error => '错误';

  @override
  String get back => '返回';

  @override
  String get screenOrientation => '屏幕方向';

  @override
  String get landscapeDisplay => '横屏显示';

  @override
  String get portraitDisplay => '竖屏显示';
}
