class SettingsModel {
  String? mqttServerAddress;
  String? mqttPort; // Added for MQTT port
  String? serverPort; // Added for Server port
  String? mqttTopic; // Added for MQTT topic
  String? groupName; // Added for group name
  String? deviceAlias;
  String? macAddress; // Changed from deviceId to macAddress
  String? deviceId; // Kept for backward compatibility during migration
  String? languageCode; // Added for i18n support
  String? screenOrientation; // Added for screen orientation

  SettingsModel({
    this.mqttServerAddress = '', // Default MQTT server
    this.mqttPort = '1883', // Default MQTT port
    this.serverPort = '8090',
    this.mqttTopic = 'esopChannel', // Default MQTT topic
    this.groupName, // Group name
    this.deviceAlias,
    this.macAddress, // MAC address for device identification
    this.deviceId, // Kept for backward compatibility
    this.languageCode = 'zh', // Default to Chinese
    this.screenOrientation = 'landscape', // Default to landscape
  });

  SettingsModel.fromJson(Map<String, dynamic> json) {
    mqttServerAddress = json['mqttServerAddress'] ?? ''; // Default MQTT server
    mqttPort = json['mqttPort'] ?? '1883'; // Default MQTT port
    serverPort = json['serverPort'] ?? '8090'; // Default Server port
    mqttTopic = json['mqttTopic'] ?? 'esopChannel'; // Default MQTT topic
    groupName = json['groupName']; // Group name
    deviceAlias = json['deviceAlias'];
    macAddress = json['macAddress']; // MAC address for device identification
    deviceId = json['deviceId']; // Kept for backward compatibility
    languageCode = json['languageCode'] ?? 'zh'; // Default to Chinese
    screenOrientation =
        json['screenOrientation'] ?? 'landscape'; // Default to landscape
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mqttServerAddress'] = mqttServerAddress;
    data['mqttPort'] = mqttPort;
    data['serverPort'] = serverPort;
    data['mqttTopic'] = mqttTopic;
    data['groupName'] = groupName;
    data['deviceAlias'] = deviceAlias;
    data['macAddress'] = macAddress; // MAC address for device identification
    data['deviceId'] = deviceId; // Kept for backward compatibility
    data['languageCode'] = languageCode;
    data['screenOrientation'] = screenOrientation;
    return data;
  }

  // Helper method to get the device identifier (MAC address preferred, fallback to device ID)
  String? getDeviceIdentifier() {
    return macAddress ?? deviceId;
  }

  // Helper method to check if MAC address is available
  bool hasMacAddress() {
    return macAddress != null && macAddress!.isNotEmpty;
  }
}
