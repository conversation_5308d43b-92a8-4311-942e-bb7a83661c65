class SettingsModel {
  String? mqttServerAddress;
  String? mqttPort; // Added for MQTT port
  String? serverPort; // Added for Server port
  String? mqttTopic; // Added for MQTT topic
  String? groupName; // Added for group name
  String? deviceAlias;
  String? deviceId;
  String? languageCode; // Added for i18n support
  String? screenOrientation; // Added for screen orientation

  SettingsModel({
    this.mqttServerAddress = '', // Default MQTT server
    this.mqttPort = '1883', // Default MQTT port
    this.serverPort = '8090',
    this.mqttTopic = 'esopChannel', // Default MQTT topic
    this.groupName, // Group name
    this.deviceAlias,
    this.deviceId,
    this.languageCode = 'zh', // Default to Chinese
    this.screenOrientation = 'landscape', // Default to landscape
  });

  SettingsModel.fromJson(Map<String, dynamic> json) {
    mqttServerAddress = json['mqttServerAddress'] ?? ''; // Default MQTT server
    mqttPort = json['mqttPort'] ?? '1883'; // Default MQTT port
    serverPort = json['serverPort'] ?? '8090'; // Default Server port
    mqttTopic = json['mqttTopic'] ?? 'esopChannel'; // Default MQTT topic
    groupName = json['groupName']; // Group name
    deviceAlias = json['deviceAlias'];
    deviceId = json['deviceId'];
    languageCode = json['languageCode'] ?? 'zh'; // Default to Chinese
    screenOrientation =
        json['screenOrientation'] ?? 'landscape'; // Default to landscape
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mqttServerAddress'] = mqttServerAddress;
    data['mqttPort'] = mqttPort;
    data['serverPort'] = serverPort;
    data['mqttTopic'] = mqttTopic;
    data['groupName'] = groupName;
    data['deviceAlias'] = deviceAlias;
    data['deviceId'] = deviceId;
    data['languageCode'] = languageCode;
    data['screenOrientation'] = screenOrientation;
    return data;
  }
}
